package com.cdkit.modules.cm.api.budget;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.budget.dto.CostQuarterlyBudgetDTO;
import com.cdkit.modules.cm.api.budget.dto.CostQuarterlyBudgetMaterialDetailDTO;
import com.cdkit.modules.cm.api.budget.dto.ProcurementPackageSubjectDTO;
import com.cdkit.modules.cm.api.budget.dto.QuarterDateRangeDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 季度预算API接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Tag(name = "季度预算管理")
public interface IQuarterlyBudgetApi {

    /**
     * 分页查询季度预算列表
     *
     * @param queryVO 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    @Operation(summary = "季度预算-分页列表查询")
    @GetMapping("/list")
    Result<IPage<CostQuarterlyBudgetDTO>> queryPageList(
            CostQuarterlyBudgetDTO queryVO,
            @Parameter(description = "页码", example = "1") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @Parameter(description = "每页数量", example = "10") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize
    );

    /**
     * 根据ID查询季度预算详情
     *
     * @param id 季度预算ID
     * @return 季度预算详情
     */
    @Operation(summary = "根据ID查询季度预算详情")
    @GetMapping("/queryById")
    Result<CostQuarterlyBudgetDTO> queryById(@Parameter(description = "季度预算ID", required = true) @RequestParam String id);

    /**
     * 新增季度预算
     *
     * @param costQuarterlyBudget 季度预算数据
     * @return 操作结果
     */
    @Operation(summary = "新增季度预算")
    @PostMapping("/add")
    Result<String> add(@RequestBody CostQuarterlyBudgetDTO costQuarterlyBudget);

    /**
     * 编辑季度预算
     *
     * @param costQuarterlyBudget 季度预算数据
     * @return 操作结果
     */
    @Operation(summary = "编辑季度预算")
    @PutMapping("/edit")
    Result<String> edit(@RequestBody CostQuarterlyBudgetDTO costQuarterlyBudget);

    /**
     * 根据ID删除季度预算
     *
     * @param id 季度预算ID
     * @return 操作结果
     */
    @Operation(summary = "根据ID删除季度预算")
    @DeleteMapping("/delete")
    Result<String> delete(@Parameter(description = "季度预算ID", required = true) @RequestParam String id);

    /**
     * 批量删除季度预算
     *
     * @param ids 季度预算ID列表，逗号分隔
     * @return 操作结果
     */
    @Operation(summary = "批量删除季度预算")
    @DeleteMapping("/deleteBatch")
    Result<String> deleteBatch(@Parameter(description = "季度预算ID列表，逗号分隔", required = true) @RequestParam String ids);

    /**
     * 生成下一个季度预算编号
     *
     * @return 下一个季度预算编号（JDYS+8位日期+3位流水）
     */
    @Operation(summary = "生成下一个季度预算编号")
    @GetMapping("/generateNextQuarterlyBudgetNo")
    Result<String> generateNextQuarterlyBudgetNo();

    /**
     * 获取季度下拉框选项
     * 返回当年四个季度和下一年第一季度的选项列表（共5个选项）
     *
     * @return 季度选项列表
     */
    @Operation(summary = "获取季度下拉框选项")
    @GetMapping("/getQuarterOptions")
    Result<List<String>> getQuarterOptions();

    /**
     * 根据季度计划ID查询原材料明细
     * 通过季度计划ID查询关联的项目计划的原材料明细数据
     * 返回参数与CostQuarterlyBudgetMaterialDetail字段一致
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 原材料明细列表
     */
    @Operation(summary = "根据季度计划ID查询原材料明细")
    @GetMapping("/queryMaterialDetailByQuarterlyPlanId")
    Result<List<CostQuarterlyBudgetMaterialDetailDTO>> queryMaterialDetailByQuarterlyPlanId(
            @Parameter(description = "季度计划ID", required = true) @RequestParam String quarterlyPlanId
    );

    /**
     * 根据选择季度计算开始结束时间
     *
     * @param quarter 季度标识（如"2025年第一季度"）
     * @return 该季度的开始日期和结束日期
     */
    @Operation(summary = "根据选择季度计算开始结束时间")
    @GetMapping("/getQuarterDateRange")
    Result<QuarterDateRangeDTO> getQuarterDateRange(@Parameter(description = "季度标识", required = true) @RequestParam String quarter);

    /**
     * 查询采办包中原材料及主要原料的预算科目信息
     * 根据季度计划ID查询关联的项目计划，获取directCostTotal作为金额基础，
     * 通过预算科目名称过滤出原材料和主要原料相关的科目
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 原材料及主要原料预算科目信息列表
     */
    @Operation(summary = "查询采办包预算科目信息", description = "查询采办包中原材料及主要原料的预算科目信息")
    @GetMapping("/queryProcurementPackageSubjects")
    Result<List<ProcurementPackageSubjectDTO>> queryProcurementPackageSubjects(
            @Parameter(description = "季度计划ID", required = true) @RequestParam String quarterlyPlanId);
}
