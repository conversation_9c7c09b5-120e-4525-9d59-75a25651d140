package com.cdkit.modules.cm.application.budget;

import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import com.cdkit.modules.cm.domain.budget.repository.CostQuarterlyBudgetRepository;
import com.cdkit.modules.cm.domain.budget.service.ProcurementPackageQueryService;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetNumberGenerationService;
import com.cdkit.modules.cm.domain.budget.service.QuarterTimeCalculationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 季度预算应用服务
 * 处理季度预算相关的业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetApplication {

    private final CostQuarterlyBudgetRepository costQuarterlyBudgetRepository;
    private final QuarterlyBudgetNumberGenerationService quarterlyBudgetNumberGenerationService;
    private final QuarterTimeCalculationService quarterTimeCalculationService;
    private final ProcurementPackageQueryService procurementPackageQueryService;

    /**
     * 分页查询季度预算列表
     *
     * @param queryEntity 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    public PageRes<CostQuarterlyBudgetEntity> queryPageList(CostQuarterlyBudgetEntity queryEntity, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照创建时间倒序
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");
        pageReq.setOrderParam(Arrays.asList(createTimeParam));

        return costQuarterlyBudgetRepository.queryPageList(queryEntity, pageReq);
    }

    /**
     * 根据ID查询季度预算详情
     *
     * @param id 季度预算ID
     * @return 季度预算实体
     */
    public CostQuarterlyBudgetEntity queryById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("季度预算ID不能为空");
        }

        CostQuarterlyBudgetEntity entity = costQuarterlyBudgetRepository.findById(id);
        if (entity == null) {
            throw new IllegalArgumentException("季度预算不存在，ID: " + id);
        }

        log.info("查询季度预算详情成功，ID: {}, 预算单号: {}", id, entity.getQuarterlyBudgetNo());
        return entity;
    }

    /**
     * 新增季度预算
     *
     * @param entity 季度预算实体
     * @return 季度预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(CostQuarterlyBudgetEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("季度预算数据不能为空");
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证季度预算单号唯一性
        if (StringUtils.hasText(entity.getQuarterlyBudgetNo())) {
            CostQuarterlyBudgetEntity existingEntity = costQuarterlyBudgetRepository.findByQuarterlyBudgetNo(entity.getQuarterlyBudgetNo());
            if (existingEntity != null) {
                throw new IllegalArgumentException("季度预算单号已存在：" + entity.getQuarterlyBudgetNo());
            }
        }

        CostQuarterlyBudgetEntity savedEntity = costQuarterlyBudgetRepository.save(entity);
        log.info("新增季度预算成功，ID: {}, 预算单号: {}", savedEntity.getId(), savedEntity.getQuarterlyBudgetNo());
        
        return savedEntity.getId();
    }

    /**
     * 编辑季度预算
     *
     * @param entity 季度预算实体
     * @return 季度预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String edit(CostQuarterlyBudgetEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("季度预算数据不能为空");
        }

        if (!StringUtils.hasText(entity.getId())) {
            throw new IllegalArgumentException("季度预算ID不能为空");
        }

        // 验证季度预算是否存在
        CostQuarterlyBudgetEntity existingEntity = costQuarterlyBudgetRepository.findById(entity.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("季度预算不存在，ID: " + entity.getId());
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证季度预算单号唯一性（排除自身）
        if (StringUtils.hasText(entity.getQuarterlyBudgetNo())) {
            CostQuarterlyBudgetEntity duplicateEntity = costQuarterlyBudgetRepository.findByQuarterlyBudgetNo(entity.getQuarterlyBudgetNo());
            if (duplicateEntity != null && !duplicateEntity.getId().equals(entity.getId())) {
                throw new IllegalArgumentException("季度预算单号已存在：" + entity.getQuarterlyBudgetNo());
            }
        }

        CostQuarterlyBudgetEntity updatedEntity = costQuarterlyBudgetRepository.updateById(entity);
        log.info("编辑季度预算成功，ID: {}, 预算单号: {}", updatedEntity.getId(), updatedEntity.getQuarterlyBudgetNo());
        
        return updatedEntity.getId();
    }

    /**
     * 根据ID删除季度预算
     *
     * @param id 季度预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("季度预算ID不能为空");
        }

        // 验证季度预算是否存在
        CostQuarterlyBudgetEntity existingEntity = costQuarterlyBudgetRepository.findById(id);
        if (existingEntity == null) {
            throw new IllegalArgumentException("季度预算不存在，ID: " + id);
        }

        costQuarterlyBudgetRepository.deleteById(id);
        log.info("删除季度预算成功，ID: {}, 预算单号: {}", id, existingEntity.getQuarterlyBudgetNo());
    }

    /**
     * 批量删除季度预算
     *
     * @param ids 季度预算ID列表，逗号分隔
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(String ids) {
        if (!StringUtils.hasText(ids)) {
            throw new IllegalArgumentException("季度预算ID列表不能为空");
        }

        List<String> idList = Arrays.asList(ids.split(","));
        
        // 验证所有ID对应的季度预算是否存在
        List<CostQuarterlyBudgetEntity> existingEntities = costQuarterlyBudgetRepository.findByIds(idList);
        if (existingEntities.size() != idList.size()) {
            throw new IllegalArgumentException("部分季度预算不存在，请检查ID列表");
        }

        costQuarterlyBudgetRepository.deleteByIds(idList);
        log.info("批量删除季度预算成功，删除数量: {}, IDs: {}", idList.size(), ids);
    }

    /**
     * 生成下一个季度预算编号
     *
     * @return 下一个季度预算编号（JDYS+8位日期+3位流水）
     */
    public String generateNextQuarterlyBudgetNo() {
        log.info("开始生成下一个季度预算编号");

        String nextBudgetNo = quarterlyBudgetNumberGenerationService.generateNextQuarterlyBudgetNo();

        log.info("生成下一个季度预算编号成功，编号: {}", nextBudgetNo);
        return nextBudgetNo;
    }

    /**
     * 获取季度下拉框选项
     * 返回当年四个季度和下一年第一季度的选项列表（共5个选项）
     *
     * @return 季度选项列表
     */
    public List<String> getQuarterOptions() {
        log.info("开始获取季度下拉框选项");

        List<String> quarterOptions = quarterTimeCalculationService.getQuarterOptions();

        log.info("获取季度下拉框选项成功，共{}个选项", quarterOptions.size());
        return quarterOptions;
    }

    /**
     * 根据选择季度计算开始结束时间
     *
     * @param quarter 季度标识（如"2025年第一季度"）
     * @return 该季度的开始日期和结束日期
     */
    public QuarterTimeCalculationService.QuarterDateRange getQuarterDateRange(String quarter) {
        log.info("开始计算季度日期范围，季度标识: {}", quarter);

        if (!StringUtils.hasText(quarter)) {
            throw new IllegalArgumentException("季度标识不能为空");
        }

        QuarterTimeCalculationService.QuarterDateRange dateRange =
                quarterTimeCalculationService.calculateQuarterDateRange(quarter);

        log.info("计算季度日期范围成功，季度: {}, 开始日期: {}, 结束日期: {}",
                quarter, dateRange.getStartDate(), dateRange.getEndDate());
        return dateRange;
    }

    /**
     * 查询采办包预算科目信息
     * 根据季度计划ID查询原材料及主要原料的预算科目信息
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 采办包预算科目信息列表
     */
    public List<ProcurementPackageQueryService.ProcurementPackageSubjectInfo> queryProcurementPackageSubjects(String quarterlyPlanId) {
        log.info("开始查询采办包预算科目信息，季度计划ID: {}", quarterlyPlanId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyPlanId)) {
                throw new IllegalArgumentException("季度计划ID不能为空");
            }

            // 调用领域服务查询
            List<ProcurementPackageQueryService.ProcurementPackageSubjectInfo> subjectInfoList =
                procurementPackageQueryService.queryProcurementPackageSubjects(quarterlyPlanId);

            log.info("查询采办包预算科目信息成功，季度计划ID: {}, 查询到{}条记录",
                    quarterlyPlanId, subjectInfoList.size());

            return subjectInfoList;

        } catch (Exception e) {
            log.error("查询采办包预算科目信息失败，季度计划ID: {}", quarterlyPlanId, e);
            throw new RuntimeException("查询采办包预算科目信息失败：" + e.getMessage());
        }
    }

    /**
     * 验证必填字段
     */
    private void validateRequiredFields(CostQuarterlyBudgetEntity entity) {
        if (!StringUtils.hasText(entity.getQuarterlyBudgetName())) {
            throw new IllegalArgumentException("季度预算名称不能为空");
        }

        if (!StringUtils.hasText(entity.getVersion())) {
            throw new IllegalArgumentException("版本不能为空");
        }

        if (!StringUtils.hasText(entity.getBudgetStatus())) {
            throw new IllegalArgumentException("预算状态不能为空");
        }
    }
}
